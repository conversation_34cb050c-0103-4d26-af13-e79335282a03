class Solution(object):
    def merge(self, nums1, m, nums2, n):
        # Initialize pointers for nums1, nums2, and the merged array
        p1 = m - 1  # Last element in nums1
        p2 = n - 1  # Last element in nums2
        p = m + n - 1  # Last position in merged array
        
        # While there are elements to compare in both arrays
        while p2 >= 0:
            # If p1 has valid elements and nums1 element is larger
            if p1 >= 0 and nums1[p1] > nums2[p2]:
                nums1[p] = nums1[p1]
                p1 -= 1
            else:
                nums1[p] = nums2[p2]
                p2 -= 1
            p -= 1