class Solution(object):
    def removeDuplicates(self, nums):
        """
        :type nums: List[int]
        :rtype: int
        """
        # index of the element to be replaced
        index = 1

        # iterate through the list
        for number in range(1, len(nums)):
            # if the current element is not equal to the previous element,
            if nums[number] != nums[number - 1]:
                # replace the element at the index with the current element
                nums[index] = nums[number]
                index += 1
        return index

nums = [0,0,1,1,1,2,2,3,3,4]
solution = Solution()
print(solution.removeDuplicates(nums))