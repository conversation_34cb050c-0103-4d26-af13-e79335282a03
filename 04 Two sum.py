class Solution(object):
    def twoSum(self, nums, target):
        """
        :type nums: List[int]
        :type target: int
        :rtype: List[int]
        """
        number_and_index = {}
        for i, num in enumerate(nums):
            complement = target - num
            if complement in number_and_index:
                return [number_and_index[complement], i]
            number_and_index[num] = i

nums = [23,85,65,21,64]
target = 85
solution = Solution()
solution.twoSum(nums, target)