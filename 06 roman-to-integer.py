class Solution(object):
    def romanToInt(self, s):
        """
        :type s: str
        :rtype: int
        """
        roman = { "I": 1, "V": 5, "X": 10, "L": 50, "C": 100, "D": 500, "M": 1000 }
        num = 0
        total = 0

        for c in s:
            if roman[c] > num:  
                total += roman[c] - 2 * num
            else:
                total += roman[c]
            num = roman[c]

        return print(total)

string = "MCMXCIV"

solution = Solution()
solution.romanToInt(string)