class Solution(object):
    def isPalindrome(self, x):
        """
        :type x: int
        :rtype: bool
        """
        
        if x < 0:
            return False
        original = x
        reversed_num = 0
        while x > 0:
            reversed_num = reversed_num * 10 + x % 10
            x //= 10
        return original == reversed_num
    
    def isPalindromeStr(self, x):
        if x < 0:
            return False
        k = str(x)
        return k == k[::-1]

num = 12345678987654321
solution = Solution()
solution.isPalindrome(num)
solution.isPalindromeStr(num)